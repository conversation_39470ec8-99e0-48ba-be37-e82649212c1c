#!/usr/bin/env python3
"""
Simple script to logout all users by clearing FCM tokens.
This approach doesn't require Redis and works with database only.
"""

import sys
import os
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from src.app.database.database import SessionLocal
from src.app.database.models import UserTokenMap
from datetime import datetime

def logout_all_users_simple():
    """
    Simple approach: Clear all FCM tokens from UserTokenMap table.
    This will force users to re-login to get new FCM tokens.
    """
    try:
        db = SessionLocal()
        
        # Count existing tokens
        token_count = db.query(UserTokenMap).count()
        print(f"Found {token_count} active tokens")
        
        # Clear all FCM tokens
        db.query(UserTokenMap).delete()
        db.commit()
        
        print(f"✅ Successfully logged out all users!")
        print(f"📊 Cleared {token_count} FCM tokens")
        print(f"🕒 Timestamp: {datetime.utcnow().isoformat()}")
        
        db.close()
        return True
        
    except Exception as e:
        print(f"❌ Error during logout: {str(e)}")
        if 'db' in locals():
            db.rollback()
            db.close()
        return False

if __name__ == "__main__":
    print("🚀 Starting global user logout...")
    success = logout_all_users_simple()
    sys.exit(0 if success else 1)
